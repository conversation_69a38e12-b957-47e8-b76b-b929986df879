'use client';
import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { useEditMode } from '../contexts/EditModeContext';
import { useDraggable } from '../hooks/useDraggable';
import { useResizable } from '../hooks/useResizable';
import { useKeyboardControls } from '../hooks/useKeyboardControls';
import { DraggableImageProps } from '../types/image';
import { DRAGGABLE_CONSTANTS, RESIZE_HANDLES, COLORS } from '../constants/draggable';
import { 
  validateImageData, 
  handleImageLoadError, 
  createFallbackImageData 
} from '../utils/imageError';
import {
  createDraggableAccessibilityProps,
  createResizeHandleAccessibilityProps,
} from '../utils/accessibility';
import { KeyboardInstructions, ResizeInstructions } from './AccessibilityInstructions';

export function DraggableImage({
  imageId,
  imageData,
  onImageChange,
  onError,
  className = '',
  style = {},
  children,
}: DraggableImageProps) {
  const {
    isEditMode,
    selectedImageId,
    setSelectedImageId,
    updateImage,
    setIsDragging,
    setDraggedImageId,
    getImage,
  } = useEditMode();

  const [isHovered, setIsHovered] = useState(false);
  const [hasImageError, setHasImageError] = useState(false);

  // グローバル画像状態をチェック
  const globalImageData = getImage(imageId);
  let currentImageData = globalImageData || imageData;

  // 画像データの検証
  const isValidImageData = validateImageData(currentImageData, imageId);
  
  // エラー時はフォールバック画像を使用
  if (!isValidImageData || hasImageError) {
    currentImageData = createFallbackImageData(imageId);
  }

  const isSelected = isEditMode && selectedImageId === imageId;
  const showControls = isEditMode && (isHovered || isSelected);

  // デフォルトの位置とサイズ
  const position = currentImageData?.position || { x: 0, y: 0 };
  const size = currentImageData?.size || DRAGGABLE_CONSTANTS.DEFAULT_SIZE;
  const zIndex = isSelected 
    ? DRAGGABLE_CONSTANTS.Z_INDEX.SELECTED 
    : (currentImageData?.zIndex || DRAGGABLE_CONSTANTS.Z_INDEX.DEFAULT);

  // カスタムフックの使用
  const {
    dragState,
    containerRef,
    handleMouseDown,
    handleTouchStart,
  } = useDraggable({
    imageId,
    currentImageData,
    isEditMode,
    onImageChange,
    updateImage,
    setSelectedImageId,
    setIsDragging,
    setDraggedImageId,
  });

  const {
    resizeState,
    handleResizeMouseDown,
  } = useResizable({
    imageId,
    currentImageData,
    isEditMode,
    onImageChange,
    updateImage,
    setSelectedImageId,
  });

  const {
    handleFocus,
    handleClick,
  } = useKeyboardControls({
    imageId,
    currentImageData,
    isSelected,
    isEditMode,
    onImageChange,
    updateImage,
    setSelectedImageId,
  });

  // 画像読み込みエラーハンドリング
  const handleImageError = useCallback(() => {
    console.warn(`Image load failed for ${imageId}:`, {
      url: currentImageData?.url,
      alt: currentImageData?.alt,
      timestamp: new Date().toISOString()
    });
    
    setHasImageError(true);
    
    // エラーコールバックがある場合のみ実行（エラーを投げない）
    if (onError) {
      try {
        handleImageLoadError(imageId, currentImageData?.url || '', onError);
      } catch (error) {
        console.error('Error in image error callback:', error);
      }
    }
  }, [imageId, currentImageData?.url, currentImageData?.alt, onError]);

  // アクセシビリティプロパティ
  const accessibilityProps = createDraggableAccessibilityProps(
    imageId,
    isSelected,
    dragState.isDragging,
    isEditMode
  );

  // リサイズハンドルの位置計算
  const getHandlePosition = useCallback((position: string): React.CSSProperties => {
    const offset = DRAGGABLE_CONSTANTS.HANDLE_OFFSET;
    
    switch (position) {
      case 'nw':
        return { top: offset, left: offset };
      case 'n':
        return { top: offset, left: '50%', transform: 'translateX(-50%)' };
      case 'ne':
        return { top: offset, right: offset };
      case 'e':
        return { top: '50%', right: offset, transform: 'translateY(-50%)' };
      case 'se':
        return { bottom: offset, right: offset };
      case 's':
        return { bottom: offset, left: '50%', transform: 'translateX(-50%)' };
      case 'sw':
        return { bottom: offset, left: offset };
      case 'w':
        return { top: '50%', left: offset, transform: 'translateY(-50%)' };
      default:
        return {};
    }
  }, []);

  // 画像データがない場合の表示
  if (!currentImageData) {
    return (
      <div
        className={className}
        style={{
          position: 'relative',
          ...style,
          cursor: isEditMode ? 'pointer' : 'default',
        }}
      >
        {children}
      </div>
    );
  }

  return (
    <>
      {/* アクセシビリティ用の説明 */}
      {isEditMode && (
        <>
          <KeyboardInstructions imageId={imageId} />
          <ResizeInstructions imageId={imageId} />
        </>
      )}
      
      <div
        ref={containerRef}
        className={className}
        style={{
          position: 'absolute',
          left: position.x,
          top: position.y,
          width: size.width,
          height: size.height,
          zIndex,
          cursor: dragState.isDragging 
            ? 'grabbing' 
            : (isEditMode ? 'grab' : 'default'),
          border: showControls 
            ? `${DRAGGABLE_CONSTANTS.BORDER_WIDTH}px solid ${COLORS.PRIMARY}` 
            : 'none',
          borderRadius: '4px',
          outline: isSelected ? `2px solid ${COLORS.PRIMARY}` : 'none',
          outlineOffset: '2px',
          transition: `all ${DRAGGABLE_CONSTANTS.ANIMATION.TRANSITION_DURATION}ms ease`,
          ...style,
        }}
        {...accessibilityProps}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onFocus={handleFocus}
        onClick={handleClick}
      >
        {/* 画像表示 */}
        <Image
          src={currentImageData.url}
          alt={currentImageData.alt}
          fill
          style={{
            objectFit: 'cover',
            borderRadius: '4px',
            pointerEvents: 'none',
            userSelect: 'none',
            opacity: hasImageError ? 0.7 : 1,
          }}
          draggable={false}
          onError={handleImageError}
          loading="lazy"
          unoptimized={currentImageData.url.startsWith('data:') || currentImageData.url.includes('blob:')}
        />

        {/* 画像エラーインジケーター */}
        {hasImageError && (
          <div
            style={{
              position: 'absolute',
              top: '8px',
              right: '8px',
              backgroundColor: '#ff5722',
              color: 'white',
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '10px',
              fontWeight: '500',
              pointerEvents: 'none',
              zIndex: DRAGGABLE_CONSTANTS.Z_INDEX.INDICATOR + 1,
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            }}
          >
            ⚠️ 読み込みエラー
          </div>
        )}

        {/* リサイズハンドル */}
        {showControls && (
          <>
            {RESIZE_HANDLES.map((handle) => {
              const handleAccessibilityProps = createResizeHandleAccessibilityProps(
                handle.position,
                imageId
              );
              
              return (
                <div
                  key={handle.position}
                  style={{
                    position: 'absolute',
                    width: `${DRAGGABLE_CONSTANTS.HANDLE_SIZE}px`,
                    height: `${DRAGGABLE_CONSTANTS.HANDLE_SIZE}px`,
                    backgroundColor: COLORS.PRIMARY,
                    border: `1px solid ${COLORS.WHITE}`,
                    borderRadius: '2px',
                    cursor: handle.cursor,
                    zIndex: DRAGGABLE_CONSTANTS.Z_INDEX.HANDLE,
                    ...getHandlePosition(handle.position)
                  }}
                  {...handleAccessibilityProps}
                  onMouseDown={(e) => handleResizeMouseDown(e, handle.position)}
                />
              );
            })}
            
            {/* 編集インジケーター */}
            <div 
              style={{
                position: 'absolute',
                top: '-32px',
                left: '0',
                backgroundColor: COLORS.PRIMARY,
                color: COLORS.WHITE,
                padding: '4px 8px',
                borderRadius: '4px',
                fontSize: '12px',
                fontWeight: '500',
                whiteSpace: 'nowrap',
                pointerEvents: 'none',
                zIndex: DRAGGABLE_CONSTANTS.Z_INDEX.INDICATOR,
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              }}
            >
              🖼️ {dragState.isDragging ? 'ドラッグ中...' : resizeState.isResizing ? 'リサイズ中...' : 'ドラッグ・リサイズ可能'}
            </div>

            {/* パフォーマンス情報（開発時のみ） */}
            {process.env.NODE_ENV === 'development' && (
              <div
                style={{
                  position: 'absolute',
                  bottom: '-20px',
                  right: '0',
                  fontSize: '10px',
                  color: '#555',
                  pointerEvents: 'none',
                }}
              >
                {size.width}×{size.height} | Z:{zIndex} {hasImageError && '| ERROR'}
              </div>
            )}
          </>
        )}

        {children}
      </div>
    </>
  );
}