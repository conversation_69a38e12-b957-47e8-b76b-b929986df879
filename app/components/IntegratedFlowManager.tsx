'use client';
import React, { useCallback } from 'react';
import { useIntegratedFlow } from '../hooks/useIntegratedFlow';
import { ConceptData } from '../types/concept';

type FlowStep = 'concept-input' | 'analyzing' | 'concept-result' | 'lp-generation' | 'lp-completed' | 'edit-mode';

interface IntegratedFlowManagerProps {
  onLPGenerated?: (concept: ConceptData) => void;
  onFlowComplete?: () => void;
}

export default function IntegratedFlowManager({
  onLPGenerated: _onLPGenerated,
  onFlowComplete: _onFlowComplete
}: IntegratedFlowManagerProps) {
  const {
    currentStep,
    isAnalyzing,
    flowState,
    handleAnalysisRequest,
    handleConceptSelect: _handleConceptSelect,
    handleLPGeneration: _handleLPGeneration,
    resetFlow
  } = useIntegratedFlow();

  const handleAnalysisComplete = useCallback(async (formData: Record<string, string>) => {
    try {
      await handleAnalysisRequest(formData);
    } catch (error) {
      console.error('分析エラー:', error);
      // エラーハンドリング: ユーザーに分かりやすいエラーメッセージを表示
    }
  }, [handleAnalysisRequest]);

  // 現在未使用だが将来の機能拡張のため保持
  // const handleConceptAdoption = useCallback(async (conceptData: ConceptData) => {
  //   try {
  //     await handleConceptSelect(conceptData);
  //     
  //     // LP生成を自動的に開始
  //     await handleLPGeneration(conceptData);
  //     
  //     if (onLPGenerated) {
  //       onLPGenerated(conceptData);
  //     }
  //     
  //     // フロー完了を通知
  //     if (onFlowComplete) {
  //       onFlowComplete();
  //     }
  //   } catch (error) {
  //     console.error('LP生成エラー:', error);
  //     // エラーハンドリング
  //   }
  // }, [handleConceptSelect, handleLPGeneration, onLPGenerated, onFlowComplete]);

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'concept-input':
      case 'analyzing':
      case 'concept-result':
        return (
          <div style={{
            padding: '40px',
            backgroundColor: '#f8f9fa',
            borderRadius: '12px',
            margin: '20px 0'
          }}>
            <h3>コンセプト入力</h3>
            <p>LP生成のための基本情報を入力してください。</p>
            <button
              onClick={() => handleAnalysisComplete({})}
              disabled={isAnalyzing}
              style={{
                padding: '12px 24px',
                backgroundColor: '#4caf50',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: isAnalyzing ? 'not-allowed' : 'pointer'
              }}
            >
              {isAnalyzing ? 'AI分析中...' : 'AIコンセプト分析を開始'}
            </button>
          </div>
        );
      
      case 'lp-generation':
        return (
          <div style={{
            padding: '40px',
            textAlign: 'center',
            backgroundColor: '#f8f9fa',
            borderRadius: '12px',
            margin: '20px 0'
          }}>
            <div style={{
              width: '60px',
              height: '60px',
              margin: '0 auto 20px',
              borderRadius: '30px',
              backgroundColor: '#4caf50',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px'
            }}>
              ⚡
            </div>
            <h3 style={{
              margin: '0 0 10px 0',
              fontSize: '20px',
              color: '#333'
            }}>
              LP生成中...
            </h3>
            <p style={{
              margin: '0 0 20px 0',
              color: '#666',
              fontSize: '14px'
            }}>
              選択されたコンセプトに基づいてランディングページを生成しています
            </p>
            <div style={{
              width: '40px',
              height: '40px',
              margin: '0 auto',
              border: '4px solid #e0e0e0',
              borderTop: '4px solid #4caf50',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }} />
          </div>
        );
      
      case 'lp-completed':
        return (
          <div style={{
            padding: '30px',
            backgroundColor: '#e8f5e8',
            borderRadius: '12px',
            margin: '20px 0',
            border: '2px solid #4caf50'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '15px'
            }}>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '20px',
                backgroundColor: '#4caf50',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '20px',
                marginRight: '12px'
              }}>
                ✅
              </div>
              <h3 
                style={{
                  margin: 0,
                  fontSize: '18px',
                  color: '#2e7d32'
                }}
                data-testid="lp-completion-title"
              >
                LP生成完了！
              </h3>
            </div>
            
            <p style={{
              margin: '0 0 20px 0',
              color: '#388e3c',
              fontSize: '14px'
            }}>
              ランディングページが生成されました。編集モードを有効にして、テキストを調整できます。
            </p>
            
            <div style={{
              display: 'flex',
              gap: '12px',
              flexWrap: 'wrap'
            }}>
              <button
                onClick={() => {
                  // 編集モードへの切り替えを外部に委託
                  document.dispatchEvent(new CustomEvent('enableEditMode'));
                }}
                style={{
                  flex: 1,
                  minWidth: '140px',
                  padding: '12px 16px',
                  backgroundColor: '#2196f3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
              >
                📝 編集モードで調整
              </button>
              
              <button
                onClick={resetFlow}
                style={{
                  padding: '12px 16px',
                  backgroundColor: '#757575',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
              >
                🔄 新しいLP作成
              </button>
            </div>
          </div>
        );
      
      case 'edit-mode':
        return (
          <div style={{
            padding: '20px',
            backgroundColor: '#e3f2fd',
            borderRadius: '12px',
            margin: '20px 0',
            border: '2px solid #2196f3'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '12px'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '16px',
                backgroundColor: '#2196f3',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px',
                marginRight: '10px'
              }}>
                ✏️
              </div>
              <h4 style={{
                margin: 0,
                fontSize: '16px',
                color: '#1976d2'
              }}>
                編集モード有効
              </h4>
            </div>
            
            <p style={{
              margin: '0 0 12px 0',
              color: '#1976d2',
              fontSize: '13px'
            }}>
              テキスト要素をクリックして編集できます。キーボードショートカット：
            </p>
            
            <ul style={{
              margin: '0',
              paddingLeft: '20px',
              fontSize: '12px',
              color: '#555'
            }}>
              <li><code>Ctrl+T</code>: テキスト編集モード切り替え</li>
              <li><code>Tab</code>: 次の編集可能要素へ移動</li>
              <li><code>Shift+Tab</code>: 前の編集可能要素へ移動</li>
              <li><code>Escape</code>: 編集をキャンセル</li>
            </ul>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div style={{
      maxWidth: '800px',
      margin: '0 auto',
      padding: '20px'
    }}>
      {/* フロー進行状況インジケーター */}
      <div style={{
        marginBottom: '30px',
        padding: '20px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <h2 style={{
          margin: '0 0 16px 0',
          fontSize: '18px',
          color: '#333'
        }}>
          🚀 LP智能化作成フロー
        </h2>
        
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          position: 'relative',
          marginBottom: '10px'
        }}>
          {/* プログレスバー背景 */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '0',
            right: '0',
            height: '2px',
            backgroundColor: '#e0e0e0',
            transform: 'translateY(-50%)',
            zIndex: 0
          }} />
          
          {/* プログレスバー進行 */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '0',
            height: '2px',
            backgroundColor: '#4caf50',
            transform: 'translateY(-50%)',
            zIndex: 1,
            width: `${flowState.progressPercentage}%`,
            transition: 'width 0.5s ease'
          }} />
          
          {/* ステップインジケーター */}
          {([
            { key: 'concept-input', label: '基本情報' },
            { key: 'analyzing', label: 'AI分析' },
            { key: 'concept-result', label: 'コンセプト確認' },
            { key: 'lp-generation', label: 'LP生成中' },
            { key: 'lp-completed', label: 'LP完成' },
            { key: 'edit-mode', label: 'テキスト調整' }
          ] as const).map((step, index) => (
            <div
              key={step.key}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                position: 'relative',
                zIndex: 2
              }}
            >
              <div style={{
                width: '24px',
                height: '24px',
                borderRadius: '12px',
                backgroundColor: flowState.completedSteps.includes(step.key as FlowStep)
                  ? '#4caf50'
                  : currentStep === step.key
                    ? '#2196f3'
                    : '#e0e0e0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '10px',
                color: 'white',
                fontWeight: '600',
                transition: 'background-color 0.3s ease'
              }}>
                {flowState.completedSteps.includes(step.key as FlowStep) ? '✓' : index + 1}
              </div>
              <span style={{
                fontSize: '11px',
                color: currentStep === step.key ? '#2196f3' : '#666',
                marginTop: '4px',
                textAlign: 'center',
                whiteSpace: 'nowrap'
              }}>
                {step.label}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* メインコンテンツ */}
      {renderCurrentStep()}

      {/* CSS アニメーション */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}