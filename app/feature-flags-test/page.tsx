// app/feature-flags-test/page.tsx
import FeatureFlagDemo from '../components/FeatureFlagDemo';

export default function FeatureFlagsTestPage() {
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            フィーチャーフラグシステム・A/Bテスト基盤
          </h1>
          <p className="text-lg text-gray-600">
            Phase 6-2: Feature Flags System & A/B Testing Infrastructure Demo
          </p>
        </div>
        
        <FeatureFlagDemo />
        
        <div className="mt-8 p-6 bg-white rounded-lg border shadow-sm">
          <h2 className="text-xl font-bold mb-4">🔧 Environment Variables</h2>
          <p className="text-gray-600 mb-4">
            The feature flags system reads from the following environment variables:
          </p>
          <div className="bg-gray-50 p-4 rounded-lg">
            <ul className="space-y-2 text-sm font-mono">
              <li><code>ENABLE_MASTRA=true</code> - Enable Mastra functionality</li>
              <li><code>MASTRA_AGENT_TOOLS=true</code> - Enable Mastra agent tools</li>
              <li><code>MASTRA_MEMORY_SYSTEM=true</code> - Enable Mastra memory system</li>
              <li><code>MASTRA_WORKFLOW_ENGINE=true</code> - Enable Mastra workflow engine</li>
              <li><code>MASTRA_AB_TEST=true</code> - Enable A/B testing</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 p-6 bg-white rounded-lg border shadow-sm">
          <h2 className="text-xl font-bold mb-4">📊 A/B Testing Logic</h2>
          <div className="space-y-4 text-gray-600">
            <p>
              • Sessions are assigned consistently to either &apos;control&apos; or &apos;mastra&apos; groups
            </p>
            <p>
              • Assignment is based on a hash of the session ID (50/50 split)
            </p>
            <p>
              • When <code>MASTRA_AB_TEST=false</code>, all users use Mastra (if enabled)
            </p>
            <p>
              • When <code>ENABLE_MASTRA=false</code>, all users use legacy system
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}