import { useState, useEffect, useCallback, useRef } from 'react';
import { ImageData, ResizeState } from '../types/image';
import { DRAGGABLE_CONSTANTS } from '../constants/draggable';

interface UseResizableProps {
  imageId: string;
  currentImageData: ImageData | null;
  isEditMode: boolean;
  onImageChange?: (imageData: ImageData) => void;
  updateImage: (imageId: string, imageData: ImageData) => void;
  setSelectedImageId: (id: string | null) => void;
}

export function useResizable({
  imageId,
  currentImageData,
  isEditMode,
  onImageChange,
  updateImage,
  setSelectedImageId,
}: UseResizableProps) {
  const [resizeState, setResizeState] = useState<ResizeState>({
    isResizing: false,
    startData: null,
  });

  const rafRef = useRef<number | null>(null);

  // アスペクト比を保持してリサイズ
  const calculateAspectRatioSize = useCallback((
    newWidth: number, 
    newHeight: number, 
    aspectRatio: number, 
    maintainAspectRatio: boolean
  ) => {
    if (!maintainAspectRatio) {
      return { width: newWidth, height: newHeight };
    }

    // より大きな変化を基準にアスペクト比を保持
    const widthRatio = newWidth / (newWidth / aspectRatio);
    const heightRatio = newHeight / (newHeight * aspectRatio);

    if (Math.abs(widthRatio - 1) > Math.abs(heightRatio - 1)) {
      return { width: newWidth, height: newWidth / aspectRatio };
    } else {
      return { width: newHeight * aspectRatio, height: newHeight };
    }
  }, []);

  // リサイズ開始
  const handleResizeMouseDown = useCallback((e: React.MouseEvent, handle: string) => {
    if (!isEditMode || !currentImageData) return;
    
    e.preventDefault();
    e.stopPropagation();

    const position = currentImageData.position || { x: 0, y: 0 };
    const size = currentImageData.size || DRAGGABLE_CONSTANTS.DEFAULT_SIZE;

    setResizeState({
      isResizing: true,
      startData: {
        position: { ...position },
        size: { ...size },
        mousePos: { x: e.clientX, y: e.clientY },
        handle,
      },
    });

    setSelectedImageId(imageId);
  }, [isEditMode, currentImageData, imageId, setSelectedImageId]);

  // サイズと位置の更新
  const updateSizeAndPosition = useCallback((
    newSize: { width: number; height: number },
    newPosition: { x: number; y: number }
  ) => {
    if (!currentImageData) return;

    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
    }

    rafRef.current = requestAnimationFrame(() => {
      const updatedImageData = {
        ...currentImageData,
        position: newPosition,
        size: newSize,
      };

      updateImage(imageId, updatedImageData);
      onImageChange?.(updatedImageData);
    });
  }, [currentImageData, imageId, updateImage, onImageChange]);

  // リサイズ処理
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!resizeState.isResizing || !resizeState.startData || !currentImageData) return;

      const { startData } = resizeState;
      const deltaX = e.clientX - startData.mousePos.x;
      const deltaY = e.clientY - startData.mousePos.y;

      let newWidth = startData.size.width;
      let newHeight = startData.size.height;
      let newX = startData.position.x;
      let newY = startData.position.y;

      const aspectRatio = startData.size.width / startData.size.height;
      const maintainAspectRatio = e.shiftKey;

      // ハンドル別のリサイズ処理
      switch (startData.handle) {
        case 'se': {
          const tempWidth = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.width + deltaX);
          const tempHeight = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.height + deltaY);
          const result = calculateAspectRatioSize(tempWidth, tempHeight, aspectRatio, maintainAspectRatio);
          newWidth = result.width;
          newHeight = result.height;
          break;
        }
        case 'sw': {
          const tempWidth = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.width - deltaX);
          const tempHeight = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.height + deltaY);
          const result = calculateAspectRatioSize(tempWidth, tempHeight, aspectRatio, maintainAspectRatio);
          newWidth = result.width;
          newHeight = result.height;
          // 幅が最小値に制限された場合、位置調整も制限
          if (tempWidth >= DRAGGABLE_CONSTANTS.MIN_SIZE) {
            newX = startData.position.x + (startData.size.width - newWidth);
          }
          break;
        }
        case 'ne': {
          const tempWidth = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.width + deltaX);
          const tempHeight = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.height - deltaY);
          const result = calculateAspectRatioSize(tempWidth, tempHeight, aspectRatio, maintainAspectRatio);
          newWidth = result.width;
          newHeight = result.height;
          // 高さが最小値に制限された場合、位置調整も制限
          if (tempHeight >= DRAGGABLE_CONSTANTS.MIN_SIZE) {
            newY = startData.position.y + (startData.size.height - newHeight);
          }
          break;
        }
        case 'nw': {
          const tempWidth = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.width - deltaX);
          const tempHeight = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.height - deltaY);
          const result = calculateAspectRatioSize(tempWidth, tempHeight, aspectRatio, maintainAspectRatio);
          newWidth = result.width;
          newHeight = result.height;
          // サイズが最小値に制限された場合、位置調整も制限
          if (tempWidth >= DRAGGABLE_CONSTANTS.MIN_SIZE) {
            newX = startData.position.x + (startData.size.width - newWidth);
          }
          if (tempHeight >= DRAGGABLE_CONSTANTS.MIN_SIZE) {
            newY = startData.position.y + (startData.size.height - newHeight);
          }
          break;
        }
        case 'e':
          newWidth = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.width + deltaX);
          break;
        case 'w':
          newWidth = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.width - deltaX);
          if (newWidth >= DRAGGABLE_CONSTANTS.MIN_SIZE) {
            newX = startData.position.x + (startData.size.width - newWidth);
          }
          break;
        case 'n':
          newHeight = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.height - deltaY);
          if (newHeight >= DRAGGABLE_CONSTANTS.MIN_SIZE) {
            newY = startData.position.y + (startData.size.height - newHeight);
          }
          break;
        case 's':
          newHeight = Math.max(DRAGGABLE_CONSTANTS.MIN_SIZE, startData.size.height + deltaY);
          break;
      }

      updateSizeAndPosition(
        { width: newWidth, height: newHeight },
        { x: newX, y: newY }
      );
    };

    const handleMouseUp = () => {
      if (resizeState.isResizing) {
        setResizeState({
          isResizing: false,
          startData: null,
        });
      }
    };

    if (resizeState.isResizing) {
      document.addEventListener('mousemove', handleMouseMove, { passive: false });
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [resizeState, currentImageData, calculateAspectRatioSize, updateSizeAndPosition]);

  // クリーンアップ
  useEffect(() => {
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, []);

  return {
    resizeState,
    handleResizeMouseDown,
  };
} 