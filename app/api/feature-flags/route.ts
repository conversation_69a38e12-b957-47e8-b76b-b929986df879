// app/api/feature-flags/route.ts
import { NextResponse } from 'next/server';
import { getFeatureFlags, DEFAULT_FEATURE_FLAGS } from '../../mastra/config/feature-flags';

export async function GET() {
  // Basic request validation could be added here if needed
  // For now, feature flags don't require authentication or parameters
  
  try {
    const flags = getFeatureFlags();
    
    return NextResponse.json(flags, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Feature flags API error:', error);
    
    // Return safe default values as fallback
    return NextResponse.json(DEFAULT_FEATURE_FLAGS, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }
}