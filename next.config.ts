import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  experimental: {
    // Existing experimental settings
  },
  // Mastra backend integration
  serverExternalPackages: ["@mastra/*"],
  // ESLint設定を緩和
  eslint: {
    ignoreDuringBuilds: true, // ESLintエラーでビルドを停止しない
  },
  typescript: {
    ignoreBuildErrors: true, // TypeScriptエラーでビルドを停止しない
  },
};

export default nextConfig;
